/* Custom CSS for <PERSON><PERSON> */

/* Global Styles */
:root {
    --primary-green: #198754;
    --light-green: #20c997;
    --dark-green: #146c43;
    --gradient-bg: linear-gradient(135deg, #198754 0%, #20c997 100%);
}

* {
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navigation */
.navbar {
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #20c997 !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: #20c997;
    transition: all 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
    left: 0;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-bg);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.hero-image {
    position: relative;
    z-index: 2;
    animation: fadeInRight 1s ease-out 0.3s both;
}

.hero-image img {
    transition: transform 0.3s ease;
}

.hero-image img:hover {
    transform: scale(1.05);
}

.hero-buttons .btn {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Products Section */
.product-card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.product-card img {
    transition: transform 0.3s ease;
    height: 250px;
    object-fit: cover;
}

.product-card:hover img {
    transform: scale(1.1);
}

.product-card .btn {
    transition: all 0.3s ease;
}

.product-card .btn:hover {
    transform: scale(1.05);
}

/* About Section */
.about-content {
    animation: fadeInLeft 1s ease-out;
}

.about-image {
    animation: fadeInRight 1s ease-out;
}

.about-image img {
    transition: transform 0.3s ease;
}

.about-image img:hover {
    transform: scale(1.02);
}

.owner-info, .business-info {
    background: rgba(25, 135, 84, 0.05);
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid var(--primary-green);
    margin-bottom: 1rem;
}

/* Contact Section */
.contact-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: rgba(25, 135, 84, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(25, 135, 84, 0.1);
    transform: translateX(5px);
}

.contact-item i {
    font-size: 1.2rem;
    margin-top: 0.2rem;
}

.map-wrapper {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.map-wrapper:hover {
    transform: scale(1.02);
}

.social-media .btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-media .btn:hover {
    background-color: var(--primary-green);
    color: white;
    transform: translateY(-3px);
}

/* Footer */
footer {
    background: var(--gradient-bg) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-buttons .btn {
        width: 100%;
        margin: 0 !important;
    }
    
    .contact-item {
        text-align: center;
        flex-direction: column;
    }
    
    .contact-item i {
        margin-bottom: 0.5rem;
    }
    
    .product-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-bg);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-green {
    background: var(--gradient-bg);
}

.shadow-custom {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-green);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top:hover {
    background: var(--dark-green);
    transform: translateY(-3px);
}

.scroll-to-top.show {
    display: flex;
}
