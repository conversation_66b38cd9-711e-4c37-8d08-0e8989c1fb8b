# Landing Page Toko <PERSON>aman Hias

Landing page responsif untuk toko tanaman hias yang mengkhususkan diri pada penjualan tanaman rumput golf dan tanaman gajah mini.

## 🌱 Fitur Utama

- **Responsif**: Tampilan optimal di semua perangkat (desktop, tablet, mobile)
- **Modern Design**: Menggunakan Bootstrap 5 dengan custom styling
- **Interactive**: Animasi smooth scrolling dan hover effects
- **SEO Friendly**: Struktur HTML yang baik untuk mesin pencari
- **Fast Loading**: Optimized images dan efficient code

## 📋 Konten Website

### 1. Hero Section
- Judul menarik dengan call-to-action
- Gambar hero yang eye-catching
- Tombol navigasi ke produk dan kontak

### 2. Produk Unggulan
- **Rumput Golf Premium**
  - Harga: Rp 25.000/m²
  - Fitur: Tahan cuaca, mudah perawatan, tekstur halus
- **Tanaman Gajah Mini**
  - Harga: Rp 45.000/pot
  - Fitur: Cocok indoor, bentuk unik, pembersih udara

### 3. Profil <PERSON>
- **Pemilik**: <PERSON><PERSON>
- **Pengalaman**: 10+ tahun di bidang tanaman hias
- **Alamat**: Jl. Raya Tanaman Hijau No. 123, Jakarta Selatan
- **Telepon**: +62 812-3456-7890
- **Jam Operasional**: Senin-Sabtu 08.00-17.00 WIB

### 4. Kontak & Lokasi
- Informasi kontak lengkap
- Google Maps terintegrasi
- Social media links
- Jam operasional

## 🛠️ Teknologi yang Digunakan

- **HTML5**: Struktur semantic yang baik
- **CSS3**: Custom styling dengan animasi modern
- **JavaScript**: Interaktivitas dan smooth scrolling
- **Bootstrap 5**: Framework CSS responsif
- **Font Awesome**: Icon library
- **Google Maps**: Integrasi peta lokasi

## 📁 Struktur File

```
├── index.html          # File HTML utama
├── style.css           # Custom CSS styling
├── script.js           # JavaScript functionality
└── README.md           # Dokumentasi
```

## 🚀 Cara Menjalankan

1. **Download/Clone** semua file ke folder lokal
2. **Buka** file `index.html` di browser
3. **Pastikan** koneksi internet aktif untuk Bootstrap CDN dan Google Maps

## 📱 Responsivitas

Website ini telah dioptimalkan untuk berbagai ukuran layar:

- **Desktop**: 1200px ke atas
- **Tablet**: 768px - 1199px  
- **Mobile**: 576px - 767px
- **Small Mobile**: di bawah 576px

## 🎨 Kustomisasi

### Mengubah Warna Tema
Edit variabel CSS di `style.css`:
```css
:root {
    --primary-green: #198754;
    --light-green: #20c997;
    --dark-green: #146c43;
}
```

### Mengubah Informasi Kontak
Edit bagian contact di `index.html`:
- Nama pemilik
- Alamat toko
- Nomor telepon
- Email
- Jam operasional

### Mengubah Google Maps
Ganti URL iframe di section contact dengan koordinat lokasi yang sesuai.

## 📞 Fitur Interaktif

### WhatsApp Integration
- Tombol "Pesan Sekarang" otomatis membuka WhatsApp
- Pesan template sudah disiapkan dengan info produk
- Nomor WhatsApp: +62 812-3456-7890

### Smooth Scrolling
- Navigasi antar section dengan animasi halus
- Active state pada menu navigation
- Scroll to top button

### Animasi
- Fade in animations saat scroll
- Hover effects pada cards dan buttons
- Loading animations

## 🔧 Optimisasi

- **Performance**: Lazy loading untuk images
- **SEO**: Meta tags dan struktur HTML yang baik
- **Accessibility**: ARIA labels dan semantic HTML
- **Cross-browser**: Compatible dengan browser modern

## 📈 Pengembangan Selanjutnya

Fitur yang bisa ditambahkan:
- [ ] Shopping cart functionality
- [ ] Payment gateway integration
- [ ] Customer testimonials
- [ ] Blog/artikel perawatan tanaman
- [ ] Live chat support
- [ ] Multi-language support

## 🐛 Troubleshooting

### Gambar Tidak Muncul
- Pastikan koneksi internet stabil
- Gambar menggunakan Unsplash API yang memerlukan internet

### Google Maps Tidak Load
- Periksa koneksi internet
- Pastikan tidak ada ad-blocker yang memblokir iframe

### Bootstrap Tidak Load
- Periksa koneksi internet untuk CDN
- Alternatif: download Bootstrap secara lokal

## 📄 Lisensi

Website ini dibuat untuk keperluan komersial toko tanaman hias. Silakan modifikasi sesuai kebutuhan.

## 👨‍💻 Developer

Dibuat dengan ❤️ menggunakan HTML, CSS, JavaScript, dan Bootstrap.

---

**Catatan**: Pastikan untuk mengganti informasi placeholder (nama, alamat, telepon, dll.) dengan data yang sesuai dengan toko Anda.
